import React from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';
import { Box, Typography, Paper } from '@mui/material';

// 注册Chart.js组件
ChartJS.register(ArcElement, Tooltip, Legend);

const AssetAreaPieChart = ({ data, title = "资产面积分布", loading = false }) => {
  // 如果没有数据，显示空状态
  if (!data || (data.selfUseArea === 0 && data.rentalArea === 0 && data.idleArea === 0)) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Typography color="text.secondary">
          暂无数据
        </Typography>
      </Paper>
    );
  }

  const chartData = {
    labels: ['自用面积', '出租面积', '闲置面积'],
    datasets: [
      {
        data: [
          data.selfUseArea || 0,
          data.rentalArea || 0,
          data.idleArea || 0,
        ],
        backgroundColor: [
          '#4CAF50', // 绿色 - 自用面积
          '#2196F3', // 蓝色 - 出租面积
          '#FF9800', // 橙色 - 闲置面积
        ],
        borderColor: [
          '#4CAF50',
          '#2196F3',
          '#FF9800',
        ],
        borderWidth: 2,
        hoverBackgroundColor: [
          '#66BB6A',
          '#42A5F5',
          '#FFB74D',
        ],
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
            return `${label}: ${value.toLocaleString()}㎡ (${percentage}%)`;
          },
        },
      },
    },
  };

  return (
    <Paper sx={{
      p: 3,
      border: loading ? '2px solid #ff9800' : '1px solid #e0e0e0',
      boxShadow: loading ? '0 4px 20px rgba(255, 152, 0, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
      transition: 'all 0.3s ease',
    }}>
      <Typography variant="h6" gutterBottom align="center">
        {title}
        {loading && (
          <Typography variant="caption" color="warning.main" sx={{ ml: 1, display: 'block' }}>
            (数据更新中...)
          </Typography>
        )}
      </Typography>
      <Box sx={{ height: 300, position: 'relative' }}>
        <Pie data={chartData} options={options} />
      </Box>
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
        <Box>
          <Typography variant="body2" color="text.secondary">
            总面积
          </Typography>
          <Typography variant="h6">
            {((data.selfUseArea || 0) + (data.rentalArea || 0) + (data.idleArea || 0)).toLocaleString()}㎡
          </Typography>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">
            利用率
          </Typography>
          <Typography
            variant="h6"
            color="primary"
            sx={{
              animation: loading ? 'pulse 2s infinite' : 'none',
              '@keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.7 },
                '100%': { opacity: 1 },
              },
            }}
          >
            {(() => {
              const total = (data.selfUseArea || 0) + (data.rentalArea || 0) + (data.idleArea || 0);
              const used = (data.selfUseArea || 0) + (data.rentalArea || 0);
              return total > 0 ? ((used / total) * 100).toFixed(1) : 0;
            })()}%
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default AssetAreaPieChart;
