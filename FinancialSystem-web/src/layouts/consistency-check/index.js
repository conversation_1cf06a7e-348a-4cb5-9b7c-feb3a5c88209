import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

const ConsistencyCheck = () => {
  const [loading, setLoading] = useState(false);
  const [report, setReport] = useState(null);
  const [error, setError] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);

  // 生成年份选项（当前年份前后3年）
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear - 3; i <= currentYear + 1; i++) {
    yearOptions.push(i);
  }

  // 月份选项
  const monthOptions = [
    { value: 1, label: '1月' },
    { value: 2, label: '2月' },
    { value: 3, label: '3月' },
    { value: 4, label: '4月' },
    { value: 5, label: '5月' },
    { value: 6, label: '6月' },
    { value: 7, label: '7月' },
    { value: 8, label: '8月' },
    { value: 9, label: '9月' },
    { value: 10, label: '10月' },
    { value: 11, label: '11月' },
    { value: 12, label: '12月' },
  ];

  // 执行一致性检查
  const performCheck = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/consistency/report?year=${selectedYear}&month=${selectedMonth}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error('检查请求失败');
      }

      const data = await response.json();
      setReport(data);
    } catch (err) {
      setError('执行一致性检查失败: ' + err.message);
      // eslint-disable-next-line no-console
      console.error('一致性检查错误:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedYear, selectedMonth]);

  // 页面加载时自动执行检查
  useEffect(() => {
    performCheck();
  }, [performCheck]);

  // 获取状态颜色
  const getStatusColor = (status) => {
    return status === '通过' ? 'success' : 'error';
  };

  // 获取状态图标
  const getStatusIcon = (status) => {
    return status === '通过' ? <CheckCircleIcon /> : <ErrorIcon />;
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={{ py: 3 }}>
          {/* 页面标题和控制区域 */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
                <Typography variant="h4" component="h1">
                  数据一致性检查
                </Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={performCheck}
                disabled={loading}
              >
                {loading ? '检查中...' : '重新检查'}
              </Button>
            </Box>

            {/* 时间选择器 */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>年份</InputLabel>
                  <Select
                    value={selectedYear}
                    label="年份"
                    onChange={(e) => setSelectedYear(e.target.value)}
                  >
                    {yearOptions.map((year) => (
                      <MenuItem key={year} value={year}>
                        {year}年
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>月份</InputLabel>
                  <Select
                    value={selectedMonth}
                    label="月份"
                    onChange={(e) => setSelectedMonth(e.target.value)}
                  >
                    {monthOptions.map((month) => (
                      <MenuItem key={month.value} value={month.value}>
                        {month.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            {/* 错误提示 */}
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* 加载状态 */}
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>正在执行数据一致性检查...</Typography>
              </Box>
            )}
          </Paper>

          {/* 检查结果 */}
          {report && !loading && (
            <>
              {/* 总体状态卡片 */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h5" gutterBottom>
                  {report.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip
                    icon={getStatusIcon(report.overallStatus)}
                    label={`总体状态: ${report.overallStatus}`}
                    color={getStatusColor(report.overallStatus)}
                    size="large"
                    sx={{ mr: 2 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    检查时间: {new Date(report.checkTime).toLocaleString()}
                  </Typography>
                </Box>
              </Paper>

              {/* 详细检查项 */}
              <Grid container spacing={3}>
                {report.checkItems && report.checkItems.map((item, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6">
                            {item.name}
                          </Typography>
                          <Chip
                            icon={getStatusIcon(item.status)}
                            label={item.status}
                            color={getStatusColor(item.status)}
                            size="small"
                          />
                        </Box>

                        {item.inconsistentCount > 0 && (
                          <Alert severity="warning" sx={{ mb: 2 }}>
                            发现 {item.inconsistentCount} 条不一致记录
                          </Alert>
                        )}

                        {/* 汇总数据 */}
                        {item.details && item.details.summary && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              汇总数据:
                            </Typography>
                            <Grid container spacing={1}>
                              <Grid item xs={6}>
                                <Typography variant="body2">
                                  新增表: {Number(item.details.summary.addTableAmount || 0).toLocaleString()}
                                </Typography>
                              </Grid>
                              <Grid item xs={6}>
                                <Typography variant="body2">
                                  诉讼表: {Number(item.details.summary.litigationAmount || 0).toLocaleString()}
                                </Typography>
                              </Grid>
                              <Grid item xs={6}>
                                <Typography variant="body2">
                                  非诉讼表: {Number(item.details.summary.nonLitigationAmount || 0).toLocaleString()}
                                </Typography>
                              </Grid>
                              <Grid item xs={6}>
                                <Typography variant="body2">
                                  减值准备表: {Number(item.details.summary.impairmentAmount || 0).toLocaleString()}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* 原始数据（调试用，可选显示） */}
              {process.env.NODE_ENV === 'development' && (
                <Paper sx={{ p: 3, mt: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    原始数据 (开发模式)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <pre style={{ fontSize: '12px', overflow: 'auto', maxHeight: '400px' }}>
                    {JSON.stringify(report.rawData, null, 2)}
                  </pre>
                </Paper>
              )}
            </>
          )}
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default ConsistencyCheck;
