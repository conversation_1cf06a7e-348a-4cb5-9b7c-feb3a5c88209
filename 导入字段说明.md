# Asset Management 数据导入字段说明

## 模板文件列表
1. `asset-basic-info-template.csv` - 资产基本信息
2. `asset-status-template.csv` - 资产状态信息  
3. `rental-info-template.csv` - 出租信息
4. `asset-financial-info-template.csv` - 资产财务信息

## 1. 资产基本信息表 (asset_basic_info)

| 列名 | 字段名 | 是否必填 | 数据类型 | 说明 | 示例 |
|------|--------|----------|----------|------|------|
| 资产名称 | asset_name | 是 | 文本(200) | 资产的名称 | 万润科技办公楼A栋 |
| 产权人ID | property_owner_id | 是 | 数字 | 关联companies表的ID | 1 |
| 管理公司 | management_company | 是 | 文本(100) | 管理该资产的公司名称 | 万润科技 |
| 权属证号 | property_certificate_no | 否 | 文本(100) | 产权证书编号 | 20241001001 |
| 是否有产权证 | has_property_certificate | 否 | 数字 | 是否有产权证(1:有 0:无) | 1 |
| 获取时间 | acquisition_date | 否 | 日期 | 资产获取日期，格式YYYY-MM-DD | 2024-01-01 |
| 购买合同编号 | purchase_contract_no | 否 | 文本(200) | 购买合同的编号 | HT202401001 |
| 购买价格 | purchase_price | 否 | 数字(20,2) | 购买价格，单位元 | ********.00 |
| 位置 | location | 否 | 文本(500) | 资产所在位置 | 上海市浦东新区张江高科技园区 |
| 总面积 | total_area | 是 | 数字(15,2) | 总面积，单位平方米 | 5000.00 |
| 产权年限 | property_years | 否 | 数字 | 产权年限 | 70 |
| 已使用年限 | used_years | 否 | 数字 | 已使用年限 | 5 |
| 剩余使用年限 | remaining_years | 否 | 数字 | 剩余使用年限 | 65 |
| 资产类型 | asset_type | 是 | 枚举 | PROPERTY(房产)/LAND(土地) | PROPERTY |
| 资产状态 | status | 否 | 枚举 | ACTIVE(正常)/INACTIVE(停用)/DISPOSED(已处置) | ACTIVE |
| 创建人 | created_by | 否 | 文本(50) | 数据创建人 | admin |
| 更新人 | updated_by | 否 | 文本(50) | 数据更新人 | admin |

## 2. 资产状态表 (asset_status)

| 列名 | 字段名 | 是否必填 | 数据类型 | 说明 | 示例 |
|------|--------|----------|----------|------|------|
| 资产ID | asset_id | 是 | 数字 | 关联资产基本信息表的ID | 1 |
| 管理公司 | management_company | 是 | 文本(100) | 管理公司名称 | 万润科技 |
| 状态年份 | status_year | 是 | 数字 | 状态记录的年份 | 2024 |
| 状态月份 | status_month | 是 | 数字 | 状态记录的月份(1-12) | 8 |
| 总面积 | total_area | 是 | 数字(15,2) | 总面积，单位平方米 | 5000.00 |
| 自用面积 | self_use_area | 否 | 数字(15,2) | 自用面积，单位平方米 | 3000.00 |
| 出租面积 | rental_area | 否 | 数字(15,2) | 出租面积，单位平方米 | 2000.00 |
| 备注 | remark | 否 | 文本 | 备注信息 | 办公楼A栋状态 |
| 创建人 | created_by | 否 | 文本(50) | 数据创建人 | admin |
| 更新人 | updated_by | 否 | 文本(50) | 数据更新人 | admin |

**注意**: 闲置面积会自动计算 = 总面积 - 自用面积 - 出租面积

## 3. 出租信息表 (rental_info)

| 列名 | 字段名 | 是否必填 | 数据类型 | 说明 | 示例 |
|------|--------|----------|----------|------|------|
| 资产ID | asset_id | 是 | 数字 | 关联资产基本信息表的ID | 1 |
| 管理公司 | management_company | 是 | 文本(100) | 管理公司名称 | 万润科技 |
| 出租人 | lessor | 是 | 文本(100) | 出租人名称 | 万润科技 |
| 承租人 | lessee | 是 | 文本(100) | 承租人名称 | 上海科技公司 |
| 合同编号 | contract_no | 否 | 文本(100) | 租赁合同编号 | ZL202401001 |
| 合同签订时间 | contract_sign_date | 否 | 日期 | 合同签订日期，格式YYYY-MM-DD | 2024-01-01 |
| 出租起始日期 | rental_start_date | 是 | 日期 | 出租开始日期，格式YYYY-MM-DD | 2024-01-01 |
| 出租截止日期 | rental_end_date | 是 | 日期 | 出租结束日期，格式YYYY-MM-DD | 2026-12-31 |
| 出租面积 | rental_area | 是 | 数字(15,2) | 出租面积，单位平方米 | 2000.00 |
| 月租金 | monthly_rent | 是 | 数字(15,2) | 月租金，单位元 | 80000.00 |
| 合同状态 | contract_status | 否 | 枚举 | ACTIVE(生效中)/EXPIRED(已到期)/TERMINATED(已终止) | ACTIVE |
| 备注 | remark | 否 | 文本 | 备注信息 | 办公楼A栋部分出租 |
| 创建人 | created_by | 否 | 文本(50) | 数据创建人 | admin |
| 更新人 | updated_by | 否 | 文本(50) | 数据更新人 | admin |

**注意**: 系统会自动检查合同是否即将到期(30天内)

## 4. 资产财务信息表 (asset_financial_info)

| 列名 | 字段名 | 是否必填 | 数据类型 | 说明 | 示例 |
|------|--------|----------|----------|------|------|
| 资产ID | asset_id | 是 | 数字 | 关联资产基本信息表的ID | 1 |
| 管理公司 | management_company | 是 | 文本(100) | 管理公司名称 | 万润科技 |
| 入账时间 | accounting_date | 是 | 日期 | 资产入账日期，格式YYYY-MM-DD | 2024-01-01 |
| 入账原值 | original_value | 是 | 数字(20,2) | 资产入账原值，单位元 | ********.00 |
| 折旧年限 | depreciation_years | 是 | 数字 | 折旧年限 | 20 |
| 折旧方法 | depreciation_method | 否 | 枚举 | STRAIGHT_LINE(直线法)/WORK_LOAD(工作量法)/DOUBLE_DECLINING_BALANCE(双倍余额递减法)/SUM_OF_YEARS(年数总和法) | STRAIGHT_LINE |
| 残值率 | residual_rate | 否 | 数字(5,4) | 残值率，默认0.05 | 0.05 |
| 财务年度 | financial_year | 是 | 数字 | 财务年度 | 2024 |
| 财务月份 | financial_month | 是 | 数字 | 财务月份(1-12) | 8 |
| 累计折旧 | accumulated_depreciation | 否 | 数字(20,2) | 累计折旧，单位元 | 500000.00 |
| 创建人 | created_by | 否 | 文本(50) | 数据创建人 | admin |
| 更新人 | updated_by | 否 | 文本(50) | 数据更新人 | admin |

**注意**: 
- 账面价值 = 入账原值 - 累计折旧，系统会自动计算
- 月折旧额会根据折旧方法自动计算
- 同一资产在同一年月只能有一条财务信息记录

## 数据导入流程建议

1. **先导入资产基本信息**: 使用 `asset-basic-info-template.csv`
2. **然后导入其他表**: 按顺序导入状态、出租、财务信息
3. **注意关联关系**: 确保资产ID在基本信息表中存在
4. **检查数据完整性**: 导入后验证数据的一致性

## 枚举值对照表

### 资产类型 (asset_type)
- `PROPERTY` - 房产
- `LAND` - 土地

### 资产状态 (status)  
- `ACTIVE` - 正常
- `INACTIVE` - 停用
- `DISPOSED` - 已处置

### 合同状态 (contract_status)
- `ACTIVE` - 生效中
- `EXPIRED` - 已到期  
- `TERMINATED` - 已终止

### 折旧方法 (depreciation_method)
- `STRAIGHT_LINE` - 直线法
- `WORK_LOAD` - 工作量法
- `DOUBLE_DECLINING_BALANCE` - 双倍余额递减法
- `SUM_OF_YEARS` - 年数总和法