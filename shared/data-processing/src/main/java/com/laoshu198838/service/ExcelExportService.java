package com.laoshu198838.service;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.aspose.cells.CalcModeType;
import com.aspose.cells.Cell;
import com.aspose.cells.Cells;
import com.aspose.cells.SaveFormat;
import com.aspose.cells.Style;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.export.ExcelExportOverdueDebt;
import com.laoshu198838.util.database.DatabaseUtils;
import com.laoshu198838.util.file.ExcelUtils;

/**
 * <AUTHOR>
 */
@Service
public class ExcelExportService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelExportService.class);

    @Autowired
    private DebtDetailsExportRepository debtDetailsExportRepository;


    public ResponseEntity<byte[]> exportNewDebtDetails(String year, String month, String company) {
        logger.info("🚀 开始导出新增债权明细表: year={}, month={}, company={}", year, month, company);
        try {
            // 获取筛选后的数据
            List<Map<String, Object>> debtDetailsList = debtDetailsExportRepository.findNewDebtDetailList(year, month, company);
            logger.info("📊 查询到新增债权明细数据: {} 条", debtDetailsList.size());

            // 加载Excel模板
            ClassPathResource templateResource = new ClassPathResource("templates/新增债权明细表模板.xlsx");
            Workbook workbook = new Workbook(templateResource.getInputStream());

            // 获取工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            Cells cells = worksheet.getCells();

            // 写入数据
            // 数据从第3行开始（行号从0开始）
            int rowIndex = 2;
            for (Map<String, Object> debtDetail : debtDetailsList) {
                // 设置序号（从1开始）
                cells.get(rowIndex, 0).setValue(rowIndex - 1);

                // 填充数据到表格
                cells.get(rowIndex, 1).setValue(debtDetail.get("管理公司"));
                cells.get(rowIndex, 2).setValue(debtDetail.get("债权人"));
                cells.get(rowIndex, 3).setValue(debtDetail.get("债务人"));
                cells.get(rowIndex, 4).setValue(debtDetail.get("新增金额"));
                cells.get(rowIndex, 5).setValue(debtDetail.get("处置金额"));
                cells.get(rowIndex, 6).setValue(debtDetail.get("债权余额"));

                // 复制样式
                if (rowIndex != 2) {
                    for (int colIndex = 0; colIndex <= cells.getMaxColumn(); colIndex++) {
                        // 获取模板中的第3行的单元格
                        Cell templateCell = cells.get(2, colIndex);
                        // 获取当前行的单元格
                        Cell currentCell = cells.get(rowIndex, colIndex);
                        // 获取模板单元格的样式
                        Style style = templateCell.getStyle();
                        // 设置当前单元格的样式
                        currentCell.setStyle(style);
                    }
                }
                rowIndex++; // 行号加1
            }

            // 设置工作簿自动计算模式
            workbook.getSettings().setCalcMode(CalcModeType.AUTOMATIC);
            
            // 强制计算所有公式
            workbook.calculateFormula();

            // 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);

            byte[] excelData = byteArrayOutputStream.toByteArray();
            logger.info("✅ 新增债权明细表导出成功，文件大小: {} 字节", excelData.length);

            // 原文件名
            String filename = "新增债权明细表.xlsx";
            // 对文件名进行 URL 编码，注意替换加号为 %20
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(excelData);

        } catch (Exception e) {
            logger.error("导出新增债权明细表Excel出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // 下载处置债权明细表
    public ResponseEntity<byte[]> exportReductionDebtDetails(String year, String month, String company) {
        logger.info("🚀 开始导出处置债权明细表: year={}, month={}, company={}", year, month, company);
        try {
            // 获取筛选后的数据
            List<Map<String, Object>> debtDetailsList = debtDetailsExportRepository.findReductionDebtDetailList(year, month, company);
            logger.info("📊 查询到处置债权明细数据: {} 条", debtDetailsList.size());

            // 加载Excel模板
            ClassPathResource templateResource = new ClassPathResource("templates/处置债权明细表模板.xlsx");
            Workbook workbook = new Workbook(templateResource.getInputStream());

            // 获取工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            Cells cells = worksheet.getCells();

            // 数据从第3行开始（Excel 以0为基准索引）
            int rowIndex = 2;
            for (Map<String, Object> debtDetail : debtDetailsList) {
                // 设置序号（从1开始）
                cells.get(rowIndex, 0).setValue(rowIndex - 1);

                // 填充数据 - 修正列顺序
                cells.get(rowIndex, 1).setValue(debtDetail.get("管理公司"));
                cells.get(rowIndex, 2).setValue(debtDetail.get("债权人"));
                cells.get(rowIndex, 3).setValue(debtDetail.get("债务人"));
                cells.get(rowIndex, 4).setValue(debtDetail.get("是否涉诉"));
                cells.get(rowIndex, 5).setValue(debtDetail.get("累计处置金额"));
                cells.get(rowIndex, 6).setValue(debtDetail.get("期间"));

                // 复制样式
                if (rowIndex != 2) {
                    for (int colIndex = 0; colIndex <= cells.getMaxColumn(); colIndex++) {
                        // 获取模板中的第3行的单元格
                        Cell templateCell = cells.get(2, colIndex);
                        // 获取当前行的单元格
                        Cell currentCell = cells.get(rowIndex, colIndex);
                        // 复制样式
                        Style style = templateCell.getStyle();
                        currentCell.setStyle(style);
                    }
                }
                rowIndex++;
            }

            // 设置工作簿自动计算模式
            workbook.getSettings().setCalcMode(CalcModeType.AUTOMATIC);
            
            // 强制计算所有公式
            workbook.calculateFormula();

            // 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);

            byte[] excelData = byteArrayOutputStream.toByteArray();
            logger.info("✅ 处置债权明细表导出成功，文件大小: {} 字节", excelData.length);

            // 设置下载的文件名，并进行 URL 编码
            String filename = "处置债权明细表.xlsx";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(excelData);

        } catch (Exception e) {
            logger.error("导出处置债权明细表Excel出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 导出完整的逾期债权清收统计表
     * 包含8个子表的完整报表，支持年份、月份和金额限制参数
     */
    public ResponseEntity<byte[]> exportCompleteOverdueReport(String year, String month, String amount) {
        try {
            logger.info("开始导出完整逾期债权清收统计表: year={}, month={}, amount={}", year, month, amount);

            // 参数验证和默认值设置
            int yearInt = (year != null && !year.isEmpty()) ? Integer.parseInt(year) : 2025;
            int monthInt = (month != null && !month.isEmpty()) ? Integer.parseInt(month) : 1;
            int amountInt = (amount != null && !amount.isEmpty()) ? Integer.parseInt(amount) : 10;

            // 调用ExcelExportOverdueDebt类的完整导出功能
            byte[] excelData = generateCompleteOverdueReport(yearInt, monthInt, amountInt);

            // 生成文件名
            String filename = String.format("%d年%02d月逾期债权清收统计表-万润科技汇总.xlsx", yearInt, monthInt);
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            logger.info("完整逾期债权清收统计表导出成功，文件大小: {} 字节", excelData.length);

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(excelData);

        } catch (Exception e) {
            logger.error("导出完整逾期债权清收统计表Excel出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .header("Content-Type", "application/json")
                    .body(String.format("{\"error\": \"导出失败: %s\"}", e.getMessage()).getBytes());
        }
    }
    
    /**
     * 生成完整的逾期债权清收统计表
     * 调用ExcelExportOverdueDebt类中的完整导出功能
     *
     * @param year 年份
     * @param month 月份
     * @param amount 金额限制（万元）
     * @return Excel文件的字节数组
     * @throws Exception 导出过程中的异常
     */
    private byte[] generateCompleteOverdueReport(int year, int month, int amount) throws Exception {
        logger.info("开始生成完整逾期债权清收统计表: year={}, month={}, amount={}", year, month, amount);

        try {
            // 直接调用而不使用反射
            com.laoshu198838.export.ExcelExportOverdueDebt exportService = new com.laoshu198838.export.ExcelExportOverdueDebt();

            // 获取数据库连接
            Connection connection = com.laoshu198838.util.database.DatabaseUtils.getConnection("overdue_debt_db");

            // 加载Excel模板
            Workbook workbook = com.laoshu198838.util.file.ExcelUtils.loadExcelTemplate("逾期债权清收统计表（模版）.xlsx");
            
            // 设置工作簿选项以确保公式自动更新
            workbook.getSettings().setCalcMode(CalcModeType.AUTOMATIC);
            workbook.getSettings().setPrecisionAsDisplayed(false);
            
            try {
                logger.info("开始导出表格3-涉诉数据");
                exportService.exportToTable3(connection, workbook, year, month);

                logger.info("开始导出表格4-非涉诉数据");
                exportService.exportToTable4(connection, workbook, year, month);

                logger.info("开始导出表格5-减值准备数据");
                exportService.exportToTable5(connection, workbook, year, month);

                logger.info("开始导出表格7-{}万元及以下应收债权明细", amount);
                exportService.exportToTable7(connection, workbook, year, month, amount);

                logger.info("开始导出表格8-临表3数据");
                exportService.exportToTable8(connection, workbook, year, month);

                logger.info("开始导出表格9-新增债权明细");
                exportService.exportToTable9(connection, workbook, year, month);

                logger.info("开始导出表格10-处置债权明细");
                exportService.exportToTable10(connection, workbook, year, month);

                // 最后强制计算所有公式
                logger.info("最终强制计算所有公式");
                workbook.calculateFormula(true);

                // 将workbook转换为字节数组
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                workbook.save(byteArrayOutputStream, SaveFormat.XLSX);
                byte[] excelData = byteArrayOutputStream.toByteArray();

                logger.info("完整逾期债权清收统计表生成成功，包含8个子表，文件大小: {} 字节", excelData.length);
                return excelData;

            } finally {
                // 确保数据库连接被关闭
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                    logger.info("数据库连接已关闭");
                }
            }

        } catch (Exception e) {
            logger.error("生成完整逾期债权清收统计表失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成完整逾期债权清收统计表失败: " + e.getMessage(), e);
        }
    }


}