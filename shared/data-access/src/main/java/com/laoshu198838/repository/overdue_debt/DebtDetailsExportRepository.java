package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 债权明细导出数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface DebtDetailsExportRepository extends JpaRepository<OverdueDebtSummary, Integer> {

    // 获取本年处置债权金额 - 用于不带筛选条件的导出，保留以兼容旧版本
    @Query(value = "SELECT \n" +
                   "    管理公司 AS companyName,\n" +
                   "    债权人 AS creditor,\n" +
                   "    债务人 AS debtor,\n" +
                   "    是否涉诉 AS isLitigated,\n" +
                   "    新增金额 AS newDebtAmount,\n" +
                   "    处置金额 AS disposalAmount,\n" +
                   "    债权余额 AS debtBalance,\n" +
                   "    期间 AS period\n" +
                   "FROM \n" +
                   "   新增表\n" +
                   // 排除新增金额为0的数据
                   "WHERE 新增金额 != 0\n" +
                   "ORDER BY 新增金额 DESC;", nativeQuery = true)
    List<Map<String, Object>> findTotalNewDebtAmount();

    // 获取新增明细表数据（支持筛选）
    @Query(value = """
                           SELECT
                               管理公司,
                               债权人,
                               债务人,
                               新增金额,
                               处置金额,
                               债权余额
                           FROM
                               新增表
                           WHERE 
                               (:year IS NULL OR 年份 = :year)
                               AND (:company IS NULL OR :company = '所有公司' OR :company = '全部' OR 管理公司 = :company)
                               AND 新增金额 > 0
                               AND (
                                   :month IS NULL 
                                   OR :month = '所有月份' 
                                   OR :month = '全部'
                                   OR (
                                       :month = '1月' AND 1月 > 0
                                       OR :month = '2月' AND 2月 > 0
                                       OR :month = '3月' AND 3月 > 0
                                       OR :month = '4月' AND 4月 > 0
                                       OR :month = '5月' AND 5月 > 0
                                       OR :month = '6月' AND 6月 > 0
                                       OR :month = '7月' AND 7月 > 0
                                       OR :month = '8月' AND 8月 > 0
                                       OR :month = '9月' AND 9月 > 0
                                       OR :month = '10月' AND 10月 > 0
                                       OR :month = '11月' AND 11月 > 0
                                       OR :month = '12月' AND 12月 > 0
                                   )
                               )
                           ORDER BY 新增金额 DESC
                   """, nativeQuery = true)
    List<Map<String, Object>> findNewDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);

    // 获取处置债权明细表数据 - 不带筛选条件，保留以兼容旧版本
    @Query(value = """
                   SELECT
                       管理公司 AS companyName,
                       债权人 AS creditor,
                       债务人 AS debtor,
                       期间 AS period,
                       是否涉诉 AS isLitigated,
                       每月处置金额 AS totalReductionAmount
                   FROM
                       处置表
                   WHERE
                       每月处置金额 != 0  -- 排除累计处置金额为0的数据
                   ORDER BY
                       每月处置金额 DESC;  -- 按照累计处置金额降序排序""", nativeQuery = true)
    List<Map<String, Object>> findTotalReductionDebtAmount();

    // 获取处置债权明细表数据（支持筛选）
    @Query(value = """
                           SELECT
                               管理公司,
                               债权人,
                               债务人,
                               是否涉诉,
                               每月处置金额 AS 累计处置金额,
                               期间
                           FROM
                               处置表
                           WHERE 
                               (:year IS NULL OR 年份 = :year)
                               AND (:company IS NULL OR :company = '所有公司' OR :company = '全部' OR 管理公司 = :company)
                               AND (:month IS NULL OR :month = '所有月份' OR :month = '全部' OR 月份 = :month)
                           ORDER BY 每月处置金额 DESC
                   """, nativeQuery = true)
    List<Map<String, Object>> findReductionDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);
}
