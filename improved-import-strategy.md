# 资产管理导入策略 - 改进方案

## 🎯 推荐方案：混合标识策略

### 方案A：自动ID + 资产编码 (推荐)
1. **资产基本信息**: 用户提供资产编码，系统自动分配数据库ID
2. **关联表**: 使用资产编码进行关联，系统内部转换为ID

### 方案B：纯自然标识 (简单易用)
使用 `管理公司 + 资产名称` 作为唯一标识，无需额外编码

## 📋 导入流程优化

### 第一步：资产基本信息导入
```csv
资产编码,资产名称,管理公司,权属证号,位置,总面积,...
WR-FC-001,万润科技办公楼A栋,万润科技,20241001001,上海浦东张江,5000.00,...
(可选，留空则自动生成：如 WR-FC-20240801001)
```

### 第二步：关联数据导入
```csv
资产标识,状态年份,状态月份,自用面积,出租面积,...
万润科技办公楼A栋,2024,8,3000.00,2000.00,...
(或使用: WR-FC-001,2024,8,3000.00,2000.00,...)
```

## 🔧 技术实现策略

### 1. 灵活匹配机制
- 优先使用资产编码匹配
- 编码为空时，使用"管理公司+资产名称"匹配
- 支持权属证号匹配（唯一时）

### 2. 导入程序逻辑
```java
// 查找资产的多种方式
Asset findAsset(String identifier, String company) {
    // 1. 先按资产编码查找
    if (isNotEmpty(identifier) && identifier.matches("[A-Z]+-[A-Z]+-\\d+")) {
        return findByAssetCode(identifier);
    }
    // 2. 按管理公司+资产名称查找
    return findByCompanyAndName(company, identifier);
}
```

### 3. 编码自动生成规则
```java
// 资产编码生成规则
String generateAssetCode(String company, String assetType) {
    String companyCode = getCompanyCode(company); // WR
    String typeCode = getAssetTypeCode(assetType); // FC/TD
    String sequence = getNextSequence(companyCode, typeCode); // 001
    return companyCode + "-" + typeCode + "-" + sequence;
}
```

## 📊 实际模板设计

### 资产基本信息模板
| 必填列 | 可选列 | 说明 |
|--------|--------|------|
| 资产名称 | 资产编码 | 编码留空则自动生成 |
| 管理公司 | 权属证号 | 用于唯一性校验 |
| 总面积 | 位置 | 核心业务字段 |
| 资产类型 | 购买价格 | 枚举值：房产/土地 |

### 关联表模板
使用 **资产名称** 作为关联字段，简单直观：
```csv
资产名称,管理公司,状态年份,状态月份,自用面积,出租面积
万润科技办公楼A栋,万润科技,2024,8,3000.00,2000.00
```

## ✨ 优势对比

| 方案 | 优势 | 适用场景 |
|------|------|----------|
| 自动ID | 无需人工编码，系统效率高 | 内部系统，无外部对接 |
| 资产编码 | 规范化管理，便于查询 | 企业级应用，多系统对接 |
| 自然标识 | 简单易懂，减少出错 | 中小企业，单一系统 |

## 🎯 最终推荐

**采用方案A：自动ID + 可选资产编码**
- 资产编码可填可不填，不填则自动生成
- 关联表使用资产名称进行匹配
- 系统内部维护ID关系，用户无感知

这样既保持了技术灵活性，又符合业务直觉！